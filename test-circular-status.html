<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Circular Status Bars Test</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        body {
            background-color: #2c2c2c;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .test-controls button {
            padding: 10px 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-controls button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Circular Status Bars Test</h1>
        
        <div class="status-bars ui-panel">
            <div class="status-bar-item">
                <div class="status-icon health">
                    <img src="images/ui_icons/heart.svg" class="status-icon-img"/>
                </div>
                <div class="status-bar-wrapper">
                    <div class="circular-progress" id="health-progress">
                        <svg viewBox="0 0 40 40">
                            <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                            <circle class="progress-fill" cx="20" cy="20" r="16" id="health-circle"></circle>
                        </svg>
                        <div class="center-icon">
                            <img src="images/ui_icons/heart.svg" alt="Health"/>
                        </div>
                    </div>
                </div>
                <div class="status-icon">
                    <img src="images/ui_icons/double-arrow-up.svg" class="status-icon-img"/>
                </div>
            </div>

            <div class="status-bar-item">
                <div class="status-icon food">
                    <img src="images/ui_icons/stomach.png" class="status-icon-img"/>
                </div>
                <div class="status-bar-wrapper">
                    <div class="circular-progress" id="food-progress">
                        <svg viewBox="0 0 40 40">
                            <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                            <circle class="progress-fill" cx="20" cy="20" r="16" id="food-circle"></circle>
                        </svg>
                        <div class="center-icon">
                            <img src="images/ui_icons/stomach.png" alt="Food"/>
                        </div>
                    </div>
                </div>
                <div class="status-icon">
                    <img src="images/ui_icons/chevron-down.svg" class="status-icon-img"/>
                </div>
            </div>

            <div class="status-bar-item">
                <div class="status-icon">
                    <img src="images/ui_icons/water.png" class="status-icon-img"/>
                </div>
                <div class="status-bar-wrapper">
                    <div class="circular-progress" id="water-progress">
                        <svg viewBox="0 0 40 40">
                            <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                            <circle class="progress-fill" cx="20" cy="20" r="16" id="water-circle"></circle>
                        </svg>
                        <div class="center-icon">
                            <img src="images/ui_icons/water.png" alt="Water"/>
                        </div>
                    </div>
                </div>
                <div class="status-icon">
                    <img src="images/ui_icons/chevron-down.svg" class="status-icon-img"/>
                </div>
            </div>

            <div class="status-bar-item">
                <div class="status-icon energy">
                    <img src="images/ui_icons/lightning.svg" class="status-icon-img"/>
                </div>
                <div class="status-bar-wrapper">
                    <div class="circular-progress" id="energy-progress">
                        <svg viewBox="0 0 40 40">
                            <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                            <circle class="progress-fill" cx="20" cy="20" r="16" id="energy-circle"></circle>
                        </svg>
                        <div class="center-icon">
                            <img src="images/ui_icons/lightning.svg" alt="Energy"/>
                        </div>
                    </div>
                </div>
                <div class="status-icon">
                    <img src="images/ui_icons/chevron-down.svg" class="status-icon-img"/>
                </div>
            </div>

            <div class="status-bar-item">
                <div class="status-icon fat">
                    <img src="images/ui_icons/muscle.png" class="status-icon-img"/>
                </div>
                <div class="status-bar-wrapper">
                    <div class="circular-progress" id="weight-progress">
                        <svg viewBox="0 0 40 40">
                            <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                            <circle class="progress-fill" cx="20" cy="20" r="16" id="weight-circle"></circle>
                        </svg>
                        <div class="center-icon">
                            <img src="images/ui_icons/muscle.png" alt="Weight"/>
                        </div>
                    </div>
                </div>
                <div class="status-icon">
                    <img src="images/ui_icons/minus.svg" class="status-icon-img"/>
                </div>
            </div>

            <div class="status-bar-item">
                <div class="status-icon sanity">
                    <img src="images/ui_icons/depression.png" class="status-icon-img"/>
                </div>
                <div class="status-bar-wrapper">
                    <div class="circular-progress" id="sanity-progress">
                        <svg viewBox="0 0 40 40">
                            <circle class="progress-bg" cx="20" cy="20" r="16"></circle>
                            <circle class="progress-fill" cx="20" cy="20" r="16" id="sanity-circle"></circle>
                        </svg>
                        <div class="center-icon">
                            <img src="images/ui_icons/depression.png" alt="Sanity"/>
                        </div>
                    </div>
                </div>
                <div class="status-icon">
                    <img src="images/ui_icons/minus.svg" class="status-icon-img"/>
                </div>
            </div>
        </div>

        <div class="test-controls">
            <button onclick="testValues(100)">Full (100%)</button>
            <button onclick="testValues(75)">High (75%)</button>
            <button onclick="testValues(50)">Medium (50%)</button>
            <button onclick="testValues(25)">Low (25%)</button>
            <button onclick="testValues(0)">Empty (0%)</button>
            <button onclick="startDemo()">Start Demo</button>
            <button onclick="stopDemo()">Stop Demo</button>
        </div>
    </div>

    <script src="status-bars-demo.js"></script>
    <script>
        let demoInterval;
        
        function testValues(percentage) {
            updateAllStatusBars({
                health: percentage,
                food: percentage,
                water: percentage,
                energy: percentage,
                weight: percentage,
                sanity: percentage
            });
        }
        
        function startDemo() {
            if (demoInterval) clearInterval(demoInterval);
            demoStatusBars();
        }
        
        function stopDemo() {
            if (demoInterval) clearInterval(demoInterval);
            testValues(100);
        }
    </script>
</body>
</html>
